<?php

use Illuminate\Support\Facades\Route;
use Illuminate\Support\Facades\Hash;
use App\Http\Controllers\UserController;
use App\Http\Controllers\PermissionController;
use App\Http\Controllers\DropzoneController;
use App\Models\Role;
use App\Models\Permission;
use GuzzleHttp\Middleware;
use Illuminate\Support\Facades\Auth;
use App\Http\Controllers\HomeController;
use App\Http\Controllers\RoleController;
use Rap2hpoutre\LaravelLogViewer\LogViewerController;

use App\Http\Controllers\ShopifyController;

Route::get('logs', [LogViewerController::class, 'index']);

// Reset Password
Route::get('profile-edit/{token}', [UserController::class, 'profileEdit'])->name('profile-edit');
Route::post('profile-update/{id}', [UserController::class, 'profileUpdate'])->name('profile-update');
// ----------------------------------------------------------------------------------------

// After Login User Can Access
Route::group(['middleware' => 'auth'], function () {
    // ----------------------------------------------------------------------------------------
    //index.file//
    Route::get('/', [HomeController::class, 'index'])->name('home');
    // ----------------------------------------------------------------------------------------

    // User Controller
    // User Add/View/Edit/Delete With Roles And Permission
    Route::group(['prefix' => 'user'], function () {
        Route::get('add', [UserController::class, 'create'])->middleware('permissioncheck:user_create')->name('user.create');
        Route::post('store', [UserController::class, 'store'])->middleware('permissioncheck:user_create')->name('user.add');
        Route::post('getuser', [UserController::class, 'getUsers'])->middleware('permissioncheck:user_list')->name('user.getData');
        Route::get('/', [UserController::class, 'show'])->middleware('permissioncheck:user_list')->name('user.list');
        Route::get('edit/{id}', [UserController::class, 'edit'])->middleware('permissioncheck:user_edit')->name('user.edit');
        Route::post('update/{id}', [UserController::class, 'update'])->middleware('permissioncheck:user_edit')->name('user.update');
        Route::post('delete', [UserController::class, 'destroy'])->middleware('permissioncheck:user_delete')->name('user.delete');

        Route::get('change-passsword', [UserController::class, 'changePassword'])->name('user.cpass');
        Route::post('update-passsword', [UserController::class, 'updatePassword'])->name('change.password');
    });

    // -------------------------- Customer management ----------------------//
    Route::group(['prefix' => 'customers'], function () {
        Route::get('/', [ShopifyController::class, 'showCustomers'])->name('customer.list');
    });

    // Product Data
    Route::group(['prefix' => 'product'], function () {
        Route::get('/', [ShopifyController::class, 'show'])->name('product.list');
    });

    // Shopify orders
    Route::get('/orders', [ShopifyController::class, 'showOrders'])->name('shopifyorders.list');
    Route::post('/orderdata', [ShopifyController::class, 'listShopifyOrders'])->name('orders.orderdata');

    // ----------------------------------------------------------------------------------------
    // Profile Edit/Update
    Route::get('my-profile', [UserController::class, 'editMyProfile'])->name('myProfile');
    Route::post('update-myprofile/{id}', [UserController::class, 'updateMyProfile'])->name('myProfile.update');
    // ----------------------------------------------------------------------------------------

    // Permission Controller
    Route::group(['prefix' => 'permission'], function () {
        Route::get('add', [PermissionController::class, 'create'])->middleware('permissioncheck:permission_create')->name('permission.create');
        Route::post('store', [PermissionController::class, 'store'])->middleware('permissioncheck:permission_create')->name('permission.add');
        Route::get('/', [PermissionController::class, 'show'])->middleware('permissioncheck:permission_list')->name('permission.list');
        Route::get('edit/{id}', [PermissionController::class, 'edit'])->middleware('permissioncheck:permission_edit')->name('permission.edit');
        Route::post('update/{id}', [PermissionController::class, 'update'])->middleware('permissioncheck:permission_edit')->name('permission.update');
        Route::post('delete', [PermissionController::class, 'destroy'])->middleware('permissioncheck:permission_delete')
            ->name('permission.delete');
    });
    // ----------------------------------------------------------------------------------------

    // Role Controller
    Route::group(['prefix' => 'role'], function () {
        Route::get('add', [RoleController::class, 'create'])->middleware('permissioncheck:role_create')->name('role.create');
        Route::post('store', [RoleController::class, 'store'])->middleware('permissioncheck:role_create')->name('role.add');
        Route::get('/', [RoleController::class, 'show'])->middleware('permissioncheck:role_list')->name('role.list');
        Route::get('edit/{id}', [RoleController::class, 'edit'])->middleware('permissioncheck:role_edit')->name('role.edit');
        Route::post('update/{id}', [RoleController::class, 'update'])->middleware('permissioncheck:role_edit')->name('role.update');
        Route::post('delete', [RoleController::class, 'destroy'])->middleware('permissioncheck:role_delete')->name('role.delete');
    });
    // ----------------------------------------------------------------------------------------

    // -------------------------- Customer management ----------------------//
    Route::group(['prefix' => 'customers'], function () {
        Route::get('/', [ShopifyController::class, 'showCustomers'])->name('customer.list');
        Route::post('customerdata', [ShopifyController::class, 'getAllCustomers'])->name('customer.customerdata');
        Route::post('getcustomerdetail-popup', [ShopifyController::class, 'getCustomerDetailPopup'])->name('customer.detailpopup');
        Route::get('edit/{id}', [ShopifyController::class, 'editCustomer'])->name('customer.edit');
        Route::post('update/{id}', [ShopifyController::class, 'updateCustomer'])->name('customer.update');
    });
});

Route::group(['prefix' => 'giveaways'], function () {
    Route::get('/', function () {
        return view('adminpanel.giveaway.view');
    })->name('giveaway.list');

    Route::post('/giveawaydata', [ShopifyController::class, 'listGiveaways'])->name('giveaway.giveawaydata');
    Route::get('/get-giveaways', [ShopifyController::class, 'getAllGiveaways']);

    Route::get('edit/{id}', [ShopifyController::class, 'editGiveaway'])->name('giveaway.edit');
    Route::post('update/{id}', [ShopifyController::class, 'updateGiveaway'])->name('giveaway.update');
});

Route::POST('fetch-draw-detail', [ShopifyController::class, 'getDrawDetail']);
Route::POST('/shopify-customer-register', [ShopifyController::class, 'shopifyCustomerRegister']);
Route::POST('/shopify-customer-activate', [ShopifyController::class, 'shopifyCustomerActivate']);
Route::GET('/get-customer-metafields', [ShopifyController::class, 'getCustomerMetafields']);
Route::get('/get-shopify-orders', [ShopifyController::class, 'getShopifyOrders']);
Route::GET('/get-customer-subscriptions', [ShopifyController::class, 'getCustomerSubscriptions']);
Route::POST('/get-bonus-detail', [ShopifyController::class, 'getBonusPoints']);
Route::POST('/apply-bonus-detail', [ShopifyController::class, 'applyBonusPoints']);
Route::POST('/membership/offer', [ShopifyController::class, 'continueMembershipOffer']);
Route::POST('/membership/cancel', [ShopifyController::class, 'cancelMembership']);
Route::POST('/get-membership-detail', [ShopifyController::class, 'getMembershipDetail']);
Route::POST('/update-membership-dates', [ShopifyController::class, 'updateMembershipDates']);

// Cron files
Route::GET('/cron-update-membership', [ShopifyController::class, 'cronUpdateMembership']);
Route::GET('/cron-update-rewardpoints', [ShopifyController::class, 'cronUpdateRewardPoints']);
Route::GET('/cron-update-bonuspoints', [ShopifyController::class, 'cronUpdateBonusPoints']);

// -------------- Stripe Routes ------------
Route::GET('/stripe-create-customer', [ShopifyController::class, 'createCustomerByEmail']);
Route::GET('/stripe-get-paymentmethods', [ShopifyController::class, 'getPaymentMethods']);
Route::GET('/stripe-make-payment', [ShopifyController::class, 'makePayment']);

Route::GET('/stripe-find-customer', [ShopifyController::class, 'findCustomerByEmail']);
Route::GET('stripe-success-url', [ShopifyController::class, 'stripeSuccess'])->name('stripe.success');
Route::GET('stripe-cancel-url', [ShopifyController::class, 'stripeCancel'])->name('stripe.cancel');
// ----------- Stripe Routes End ------------


Route::POST('check-payment-method', [ShopifyController::class, 'checkPaymentMethod']);
Route::POST('update-card-details', [ShopifyController::class, 'updateCardDetails']);
Route::GET('test-mail', [ShopifyController::class, 'testMail']);

Auth::routes(['register' => false]);
