{% comment %}
{{ 'customer.css' | asset_url | stylesheet_tag }}
{% endcomment %}

{%- style -%}
  .section-{{ section.id }}-padding {
    padding-top: {{ section.settings.padding_top | times: 0.75 | round: 0 }}px;
    padding-bottom: {{ section.settings.padding_bottom | times: 0.75 | round: 0 }}px;
  }

  /* added */
  .nr_customer{
    padding: 95px 7vw 0;
    font-family: GlamourAbsolute_Regular; 
  }

  #customer_profile, #customer_profile h2{
    display:block;
    color:#FFFFFF!important;
  }

  #order_detail{
    color:#FFFFFF!important;
  }
  
  /* .nr_customer, .nr_customer a, .nr_customer h2{
    color:#FFFFFF!important;
  } */

  /* CSS ADDED FOR HEADER  */

  .nr_customer_account_wrapper{
    padding: 70px 0;
    max-width: 100%;
  }

  .nr_menu_divider{
    width: 100%;
    height: 1px;
    background: linear-gradient(180deg, #BF34FB, #552EFA);
    display: block;
    margin: 24px 0;
  }

  .nr_nav_wrapper{
    display: flex;
    gap: 16px;
  }

  .nr_nav_link{
    background-color: #FFFFFF;
    padding: 18px 45px;
    min-width: 210px;
    text-decoration: none;
    border-radius: 20px;
    display: flex;
    justify-content: center;
    align-items: center;
    transition: all 0.4s ease-in-out;
    position: relative;
  }

  .nr_nav_link:hover,
  .nr_nav_link.active{
    background: linear-gradient(180deg, #FFFFFF, #E8AB1C);
  }

  .nr_nav_link::before{
    content: "";
    position: absolute;
    inset: 0;
    border-radius: 20px;
    padding: 1px;
    background: linear-gradient(180deg, #BF34FB, #552EFA);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }

  .nr_nav_link span{
    font-size: 18px;
    line-height: normal;
    font-weight: 400;
    background-image: linear-gradient(180deg, #00000066, #00000066, #00000066);
    color: transparent;
    background-clip: text;
    transition: all 0.4s ease-in-out;
  }

  .nr_nav_link:hover span, 
  .nr_nav_link.active span{
    background-image: linear-gradient(180deg, #3D3299, #5C2F8E, #231B59);
    color: transparent;
    background-clip: text;
  }

  /* CSS ADDED FOR HEADER  */

  /* CSS ADDED FOR DASHBOARD CONTENT START*/

  .nr_common_text_color{
    background: linear-gradient(180deg, #FFFFFF, #E8AB1C);
    color: transparent;
    background-clip: text;
  }

  .nr_common_text_color_white{
    background: linear-gradient(180deg, #FFFFFF, #c6b690);
    color: transparent;
    background-clip: text;
    font-size: 28px;
    line-height: 34px;
  }

  .nr_common_text_color_yellow {
    background: linear-gradient(180deg, #cfac5a, #E8AB1C);
    color: transparent;
    background-clip: text;
    font-size: 28px;
    line-height: 34px;
  }

  .nr_pgb_gradient_border{
    position: relative;
  }

  .nr_pgb_gradient_border::before{
    content: "";
    position: absolute;
    inset: 0;
    padding: 2px;
    background: linear-gradient(to bottom right, #BF34FB, #54ECE9, #552EFA);
    mask: linear-gradient(#000 0 0) content-box, linear-gradient(#000 0 0);
    mask-composite: exclude;
  }
  
  .nr_right_div{
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 36px;
  }

  .nr_header_wrappper{
    display: flex;
    align-items: flex-end;
    justify-content: space-between;
  }

  .nr_profile_name_wrapper{
    display: flex;
    flex-direction: column;
    gap: 10px;
  }

  .nr_profilename_text{
    font-size: 58px;
    line-height: 60px;
    font-weight: 400;
  }

  .nr_customer_name{
    display: flex;
    align-items: center;
    gap: 12px;
  }

  .nr_profilename_icon{
    width: 54px;
    height: 54px;
    background-color: #393F73;
    padding: 6px;
    display: inline-block;
    border-radius: 100%;
  }

  .nr_profilename_icon img{
    width: 100%;
    height: 100%;
  }

  /* .nr_dashboard_divider{
    margin: 0 !important;
  } */

  .nr_dashboard_title{
    font-size: 28px;
    line-height: 30px;
    margin-bottom: 30px;
    display: block;
  }

  .nr_dashboard_details_wrapper{
    display: flex;
    gap: 15px;
  }

  .nr_noble_card{
    max-width: 391px;
    border-radius: 13px;
    flex: 0 0 auto;
  }

  .nr_noble_card img{
    width: 100%;
    height: 100%;
  }

  .nr_membership_card{
    width: 391px;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 100%;
    background: linear-gradient(360deg, rgba(255, 255, 255, 0.16) 100%, rgba(255, 255, 255, 0.26) 100%);
    padding: 20px 28px 20px 28px;
    position: relative;
    z-index: 1;
    border-radius: 13px;
  }

  .nr_membership_card span{
    font-size: 24px;
    line-height: 30px;
  }

  .nr_noble_card::before{
    border-radius: 13px;
  }

  .nr_dashboard_points_wrapper{
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .nr_points_card{
    background: linear-gradient(360deg, rgba(255, 255, 255, 0.16) 100%, rgba(255, 255, 255, 0.26) 100%);
    padding: 20px 28px 20px 28px;
    border-radius: 26px;
    position: relative;
    z-index: 1;
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: 35px;
    overflow: hidden;
  }

  .nr_points_card::before{
    border-radius: 26px;
  }

  .nr_points_card::after{
    content: "";
    background-color: transparent;
    background: transparent;
    backdrop-filter: blur(4px);
    -webkit-backdrop-filter: blur(4px);
    width: 100%;
    height: 100%;
    position: absolute;
    top: 0;
    left: 0;
    z-index: -9;
  }

  .nr_points_card_title_wrapper{
    display: flex;
    flex-direction: column;
  }

  .nr_points{
    font-size: 66px;
    line-height: 70px;
  }

  .nr_club_card_inner{
    display: flex;
    flex-direction: column;
    align-items: start;
    gap: 39px;
  }

  .nr_club_card_content{
    display: flex;
    flex-direction: column;
    gap: 5px;
  }

  .nr_club_card_title{
    font-size: 28px;
    line-height: 36px;
  }

  .nr_club_card_subtitle{
    font-size: 18px;
    line-height: 27px;
    color: #FFF;
  }

  .nr_club_btn{
    position: relative;
    background: linear-gradient(180deg, #3D3299, #5C2F8E, #231B59);
    color: transparent;
    background-clip: text;
    padding: 15px 26px;
    border: 0;
    border-radius: 10px;
    font-size: 18px;
    line-height: 22px;
    min-width: 220px;
    height: 52px;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .nr_club_btn::before{
    content: '';
    background-color: #FFE99A;
    position: absolute;
    width: 100%;
    height: 100%;
    top: 0;
    left: 0;
    z-index: -1;
    border-radius: 10px;
  }

  .nr_club_diamond_wrapper{
    background-color: #FDD95B;
    border-radius: 42px;
    min-width: 167px;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 24px;
  }

  .nr_club_diamond_inner{
    width: 120px;
    height: 120px;
    padding: 14px;
    background-color: #393F73;
    border-radius: 100%;
  }

  .nr_club_diamond_inner img{
    width: 100%;
    height: 100%;
  }

  .nr_club_card_diamond_mobile{
    display: none;
  }

  .nr_order_table_wrapper{
    position: relative;
    z-index: 5;
    width: 100%;
  }

  .nr_order_table_wrapper .order-history{
    width: 100%;
    margin-top: 18px;
  }

  .nr_order_table_wrapper .order-history thead tr{
    width: 100%;
    height: 1px;
    background: linear-gradient(180deg, #FFFFFF, #E8AB1C);
  }

  .nr_order_table_wrapper .order-history thead tr th{
    padding: 16px;
    background: linear-gradient(180deg, #3D3299, #5C2F8E, #231B59);
    color: transparent;
    background-clip: text;
    font-size: 24px;
    line-height: 28px;
    text-wrap: nowrap;
  }

  .nr_order_table_wrapper .order-history tbody tr td{
    padding: 20px 16px;
    font-size: 20px;
    line-height: 26px;
    text-wrap: nowrap;
    color:#FFF!important;
  }

  .nr_order_table_wrapper .order-history tbody tr{
    border-bottom: 1px solid #c2b89f63;
  }

  .nr_order_table_wrapper .order-history tbody tr:nth-last-child(1){
    border-bottom: 0;
  }

  .nr_order_table_wrapper .order-history tbody tr td .nr_table_link{
    color: #FFF !important;
  }
  
  /* CSS ADDED FOR RIGHT DASHBOARD CONTENT END*/

  /* CSS ADDED FOR SHOP LIST CARD */

  .shop-card-list-wrapper{
    display: flex;
    flex-direction: column;
    gap: 24px;
  }

  .nr-shop-list-card{
    gap: 46px;
    justify-content: flex-start;
    align-items: flex-start;
  }

  .nr-shop-list-img img{
    border-radius: 36px;
  }

  .nr-shop-list-title{
    font-size: 28px;
    line-height: 32px;
    display: inline-block;
  }

  .nr-shop-list-ul{
    list-style-type: none;
    margin: 0;
    padding: 6px 0 24px;
  }

  .nr-shop-list-ul li{
    font-size: 18px;
    line-height: 26px;
    color: #FFF;
  }

  .nr-shop-card-btn{
    min-width: auto;
  }

  .nr_select{
    display: flex;
    position: relative;
  }

  .nr-address-select{
     width: 100%;
    height: 52px !important;
    background: linear-gradient(180deg, #bf34fb42, #552efa42);
    border: 2px #BF34FB solid !important;
    border-radius: 8px !important;
    color: #fff !important;
    padding: 0 40px 0 15px !important;
    font-size: 16px !important;
    position: relative;
    appearance: none;
    webkit-appearance: none;
   }

  .nr-address-select:focus{
    box-shadow: none !important;
    outline: none !important;
  }

   .nr-address-select option{
     color: #fff !important;
     background: #1c163a;
   }

  .nr-address-select-svg{
    right: 12px !important;
    position: absolute;
    top: calc(50% - 7px);
    width: 13px;
    height: 12px;
    display: flex;
  }

   .nr-address-select-svg svg{
     position: relative !important;
     top: 0 !important;
     left: 0 !important;
     max-width: 15px;
     color: #FFF;
   }
  

  /***************** RESPONSIVE CSS ADDED START **************/

  @media (max-width: 1599.98px){

     /* CSS ADDED FOR HEADER  */
    .nr_profilename_text {
      font-size: 50px;
      line-height: 54px;
    }
    .nr_nav_wrapper {
      gap: 12px;
    }
    .nr_nav_link{
      min-width: 180px;
    }
  }

  @media (max-width: 1399.98px){

    /* CSS ADDED FOR HEADER  */
    .nr_profilename_text {
      font-size: 48px;
      line-height: 48px;
    }
    .nr_nav_wrapper {
      gap: 10px;
    }
    .nr_nav_link{
      min-width: 140px;
      padding: 14px 30px;
    }
    

    /* CSS ADDED FOR DASHBOARD CONTENT START*/
    .nr_right_div {
      gap: 28px;
    }
    .nr_noble_card {
      max-width: 330px;
    }
    .nr_membership_card {
      width: 330px;
    }
    .nr_dashboard_points_wrapper {
      gap: 20px;
    }
    .nr_points_card {
      padding: 18px 28px 18px 28px;
    }
    .nr_common_text_color_white {
      font-size: 22px;
      line-height: 34px;
    }
    .nr_common_text_color_yellow {
      font-size: 22px;
      line-height: 34px;
    }
    .nr_points {
      font-size: 56px;
      line-height: 62px;
    }
    .nr_club_card_subtitle {
      font-size: 18px;
      line-height: 24px;
    }
    .nr_club_btn{
    font-size: 16px;
    line-height: 20px;
    }
    
  }

  @media (max-width: 1199.98px){

    /* CSS ADDED FOR HEADER  */
    .nr_profile_name_wrapper {
      gap: 4px;
    }

    .nr_profilename_text {
      font-size: 35px;
      line-height: 35px;
    }
    .nr_customer_name {
      gap: 8px;
    }
    .nr_profilename_icon {
      width: 47px;
      height: 47px;
    }
    .nr_nav_link {
      padding: 14px 26px;
      min-width: 126px;
    }
    .nr_nav_link span {
      font-size: 14px;
    }

    /* CSS ADDED FOR DASHBOARD CONTENT START*/
    .nr_dashboard_title {
      font-size: 26px;
      line-height: 24px;
      margin-bottom: 24px;
    }
    
    .nr_dashboard_details_wrapper {
      gap: 20px;
    }

    .nr_club_diamond_wrapper {
      min-width: 150px;
      padding: 24px;
    }

    .nr_club_diamond_inner {
      width: 100px;
      height: 100px;
      padding: 14px;
    }

    .nr_order_table_wrapper .order-history {
      width: 100%;
      min-width: 700px;
    }

    .nr_order_table_wrapper .order-history thead tr th{
      font-size: 20px;
      line-height: 26px;
    }
    .nr_order_table_wrapper .order-history tbody tr td {
      font-size: 18px;
      line-height: 22px;
    }
    
  }

  @media (max-width: 991.98px){

    /* CSS ADDED FOR HEADER  */

    .nr_header_wrappper{
      flex-direction: column;
      align-items: flex-start;
      gap: 16px;
    }

    .nr_nav_wrapper{
      width: 100%;
    }

    .nr_nav_link{
     width: 100%;
     min-width: unset;
    }

    /* CSS ADDED FOR DASHBOARD CONTENT START*/
    
    .nr_dashboard_details_wrapper {
      flex-direction: column;
    }

    .nr_membership_card{
      min-height: 207px;
      max-height: 207px;
    }
    
    /* .nr_club_btn{
      display: none;
    } */
    .nr_club_diamond_wrapper{
      display: none;
    }

    .nr_club_card{
      flex-direction: column;
      gap: 20px;
    }
    
    .nr_club_card_diamond_mobile{
      display: flex;
      align-items: center;
      justify-content: space-between;
      width: 100%;
    }

    .nr_club_card_diamond_mobile .nr_club_btn{
      display: block;
      padding: 12px 15px;
    }

    .nr_club_card_diamond_mobile .nr_club_diamond_wrapper{
      display: flex;
      min-width: 80px;
      padding: 14px;
      border-radius: 30px;
    }

    .nr_club_card_diamond_mobile .nr_club_diamond_wrapper .nr_club_diamond_inner{
      width: 60px;
      height: 60px;
      display: flex;
      padding: 9px;
    }

    .nr_order_table_wrapper .nr_order_table{
      overflow: auto;
    }
  }

  @media (max-width: 767px){

    .nr_club_btn {
        padding: 12px;
      height: 48px;
    }

     .nr-address-select{
       height: 48px !important;
     }
    /* CSS ADDED FOR SHOP LIST CARD */
  
    .nr-shop-list-card{
      gap: 26px;
      justify-content: flex-start;
      align-items: flex-start;
      flex-direction: column;
    }

    .nr-shop-card-btn{
      display: block !important;
    }

    .nr-shop-list-title {
      font-size: 24px;
      line-height: 30px;
    }

    .nr-shop-list-ul li {
      font-size: 16px;
      line-height: 24px;
    }
  }

  @media (max-width: 575px){

    /* CSS ADDED FOR HEADER  */

    .nr_nav_wrapper {
      gap: 5px;
    }
    
    .nr_nav_link {
      min-width: 75px;
      padding: 10px;
    }
    
    .nr_nav_link span {
      font-size: 12px;
    }

    /* CSS ADDED FOR DASHBOARD CONTENT START*/

    .nr_common_text_color_white {
      font-size: 20px;
      line-height: 28px;
    }
    .nr_common_text_color_yellow {
      font-size: 20px;
      line-height: 28px;
    }

    .nr_membership_card {
      width: 100%;
    }
    .nr_points_card {
      padding: 18px;
    }
    .nr_club_card_title {
      font-size: 25px;
      line-height: 32px;
    }
    .nr_club_card_subtitle {
      font-size: 16px;
      line-height: 21px;
    }
    .nr_club_btn {
      font-size: 14px;
      line-height: 18px;
    }
    .nr_club_card_diamond_mobile .nr_club_btn {
      padding: 10px 12px;
    }

    .nr_order_table_wrapper .order-history thead tr th{
      padding: 14px;
      font-size: 16px;
      line-height: 20px;
    }

    .nr_order_table_wrapper .order-history tbody tr td{
      padding: 18px 14px;
      font-size: 16px;
      line-height: 20px;
    }

    .nr_club_btn {
       width: 100%;
    }

    .nr_select{
      width: 100%;
    }
    
  }
  /* ended */

  .nr_empty_order_history{
    font-size: 20px;
  }

  /* @media screen and (min-width: 750px) {
    .section-{{ section.id }}-padding {
      padding-top: {{ section.settings.padding_top }}px;
      padding-bottom: {{ section.settings.padding_bottom }}px;
    }
  } */

  #divBonusPoints{
    display:none;
  }
{%- endstyle -%}
<section class="nr_customer register section-{{ section.id }}-padding">
  <div class="nr_customer_account_wrapper">
    <!-- <div class="nr_customer_header">
      <h1 class="customer__title">{{ 'customer.account.title' | t }}</h1>
      <a href="{{ routes.account_logout_url }} class="d-flex">
        <span class="svg-wrapper">
          {{- 'icon-account.svg' | inline_asset_content -}}
        </span>
          {{ 'customer.log_out' | t }}
      </a>
    </div> -->
    <div class="nr_account_wrapper">
      <!-- <div class="nr_left_div">
        <div class="nr_account_dashboard_menu_wrapper">
          <div class="nr_account_profile_wrapper">
           <div class="nr_profile_img_wrapper">
              <img width="218" height="218" class="nr_profile_img" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/profile-image.png?v=**********">
              <div class="nr_upload_profile_wrapper">
                <img width="20" height="20" class="nr_profile_upload_icon" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/upload-icon.svg?v=**********">
              </div>
           </div>
            <span class="nr_profile_name">Richard Sentnar</span>
          </div>
          <span class="nr_menu_divider"></span>
          <div class="nr_nav_wrapper">
          <a href="#" class="nr_nav_link active">
            <span>Dashboard</span>
          </a>
          <a href="#" class="nr_nav_link">
            <span>Partners’ Discount</span>
          </a>
          <a href="#" class="nr_nav_link">
            <span>Shop</span>
          </a>
          <a href="{{ routes.account_addresses_url }}" class="nr_nav_link">
            <span>Profile</span>
          </a>
          </div>
        </div>
        <div class="nr_logout_wrapper">
          <a class="nr_logout_link" href="{{ routes.account_logout_url }}">
            <span>Log Out</span>
          </a>
        </div>
      </div> -->

      <div class="nr_right_div" id="customer_membership">
        <div class="nr_header_wrappper">
          <div class="nr_profile_name_wrapper">
            <span class="nr_profilename_text nr_common_text_color">Hello</span>
              <div class="nr_customer_name">
                <span class="nr_profilename_text nr_common_text_color">{{ customer.first_name }} {{ customer.last_name }}</span>
                <!-- <span class="nr_profilename_icon">
                 <img width="41" height="41" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/profile-dimond-icon.png?v=**********">
                </span> -->
              </div>
          </div>
          <div class="nr_nav_wrapper">
            <a href="#" class="nr_nav_link active">
              <span>Dashboard</span>
            </a>
            <a href="/collections/all" class="nr_nav_link">
              <span>Shop</span>
            </a>
            <a href="{{ routes.account_addresses_url }}" class="nr_nav_link">
              <span>Profile</span>
            </a>
            <a href="{{ routes.account_logout_url }}" class="nr_nav_link">
              <span>Logout</span>
            </a>
          </div>
        </div>
        <span class="nr_dashboard_divider nr_menu_divider"></span>
        <div class="nr_dashboard_wrapper">
          <span class="nr_dashboard_title nr_common_text_color">Your Dashboard</span>
          <div class="nr_dashboard_details_wrapper">
            <div class="nr_noble_card nr_pgb_gradient_border">
              {% assign membership_status = '' %}
                {% if customer.metafields.custom.active_membership != '' %}
                    {% assign membership_status = customer.metafields.custom.active_membership %}
                {% endif %}
                {% if customer and membership_status contains "Black Membership" %}
                     <img width="391" height="246" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/black_membership.webp?v=**********">
                {% elsif customer and membership_status contains "Platinum Membership" %}
                     <img width="391" height="246" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/platinum_membership.webp?v=1740549707">
                {% elsif customer and membership_status contains "Prestige Membership" %}
                     <img width="391" height="246" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/Prestige_Membership.webp?v=1739948470">
                {% else %}
                  <div class="nr_membership_card">
                    <span class="nr_common_text_color">
                      No Membership
                    </span>
                  </div>
                  <!-- <img width="391" height="246" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/dashboard_card.png?v=1741183586"> -->
                {% endif %}
            </div>
            <div class="nr_dashboard_points_wrapper">
               <div class="nr_points_card nr_pgb_gradient_border">
                  <div class="nr_points_card_title_wrapper">
                    <span class="nr_points_card_title nr_common_text_color_white">Your Available</span>
                    <span class="nr_points_card_title nr_common_text_color_yellow">Entries</span>
                  </div>
                  <span class="nr_points nr_common_text_color">
                    {% if customer.metafields.custom.total_entries %}
                      {{ customer.metafields.custom.total_entries }}
                    {% else %}
                      0
                    {% endif %}
                  </span>
               </div> 
              <div class="nr_points_card nr_pgb_gradient_border">
                  <div class="nr_points_card_title_wrapper">
                    <span class="nr_points_card_title nr_common_text_color_white">Your Participated</span>
                    <span class="nr_points_card_title nr_common_text_color_yellow">Contests</span>
                  </div>
                  <span class="nr_points nr_common_text_color">0</span>
               </div> 
            </div>
          </div>
        </div>
        <span class="nr_dashboard_divider nr_menu_divider"></span>
        <div class="nr_points_card nr_club_card nr_pgb_gradient_border">
            <div class="nr_club_card_inner">
              <div class="nr_club_card_content">
                <span class="nr_club_card_title nr_common_text_color">
                  {% if membership_status == blank %}
                    You have no membership at the moment
                  {% else %}
                    You have a {{ membership_status }}
                  {% endif %}
                </span>
                <span class="nr_club_card_subtitle">Upgrade your membership to get more perks and more benefits with high chances of winning! </span>
              </div>
              <div class="d-flex flex-column flex-sm-row gap-4 w-100">
                {% if customer %}
                  {% if customer.tags contains "Black Membership" or customer.tags contains "Platinum Membership" or customer.tags contains "Prestige Membership" %}
                    <a class="nr_club_btn" href="{{ routes.root_url }}#tab_default_2" style="width:300px!important;">Upgrade Your Membership</a>
                    <span id="update-membership-date">
                      <a class="nr_club_btn" href="javascript:;" style="width:350px!important;" onclick='updateMembershipDates()'>Update Upcoming Membership</a> <i style='color:#FFF!important;'>(This option will update membership dates, membership & bonus points accordingly and it's for temporary & testing purpose only)</i>
                    </span>
                  {% else %}
                    <a class="nr_club_btn" href="{{ routes.root_url }}#tab_default_2">Subscribe Membership</a>
                  {% endif %}
                 
                {% endif %}
              </div>

              <div id="divBonusPoints">
                <div class="nr_club_card_content">
                  <span class="nr_club_card_title nr_common_text_color">
                    Your Total Bonus Points : <span id='receivedBonuPoint'></span>
                  </span>
                </div>
                <div class="d-flex flex-column flex-sm-row align-items-center gap-4 w-100">
                  <div class="nr_select">
                   <input type="hidden" name="appBonusPoints" id='appBonusPoints'>
                   <select name="bonus_applicable_on" id="bonus_applicable_on" class="nr-address-select">
                    <option value="">Select bonus applicable month</option>
                    {% assign month_names = "January,February,March,April,May,June,July,August,September,October,November,December" | split: "," %}
                    {% assign current_year = 'now' | date: "%Y" %}
                    {% assign current_month = 'now' | date: "%-m" %}
                    {% for i in (0..5) %}
                      {% assign month_index = current_month | plus: i | minus: 1 %}
                      {% assign year_offset = month_index | divided_by: 12 %}
                      {% assign display_month_index = month_index | modulo: 12 %}
                      {% assign month_name = month_names[display_month_index] %}
                      {% assign year = current_year | plus: year_offset %}
                      <option value="{{ month_name }} {{ year }}">{{ month_name }} {{ year }}</option>
                    {% endfor %}
                  </select>
                  <span class="nr-address-select-svg">
                    {{- 'icon-caret.svg' | inline_asset_content -}}
                  </span>
                </div>
                <button id="btnApplyBonusPoints" class="nr_club_btn" onclick="applyBonusPoints()">Use Bonus Points</button>
                </div>
              </div>
            </div>
          
            <!-- <div class="nr_club_diamond_wrapper">
              <span class="nr_club_diamond_inner">
                <img width="92" height="92" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/club-card-diamond-icon.png?v=1741191794">
              </span>
            </div> -->
            
          <div class="nr_club_card_diamond_mobile">
                <!-- <button class="nr_club_btn">Upgrade Your Membership</button>
                <div class="nr_club_diamond_wrapper">
                  <span class="nr_club_diamond_inner">
                    <img width="92" height="92" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/club-card-diamond-icon.png?v=1741191794">
                  </span>
                </div> -->
                <!-- <a class="nr_club_btn" href="{{ routes.root_url }}#tab_default_2">Upgrade Your Membership</a> -->
          </div>
        </div> 
      </div>
      
      <span class="nr_dashboard_divider nr_menu_divider"></span>
      
    <!-- SHOP CARD LIST START -->
      <div class="shop-card-list-wrapper">
        <span class="nr_dashboard_title nr_common_text_color mb-0">Partner's Discount</span>
        <div class="nr_points_card nr_pgb_gradient_border nr-shop-list-card">
          <div class="nr-shop-list-img">
            <img width="140" height="140" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/shop-list4.png?v=1741691267">
          </div>
          <div class="nr-shop-list-card-body">
            <span class="nr-shop-list-title nr_common_text_color">Maid To Order</span>
            <ul class="nr-shop-list-ul">
              <li>
                <span class="nr_common_text_color">IG:</span>
                <span>@maidtoorder_au</span>
              </li>
              <li>
                <span class="nr_common_text_color">Location:</span>
                <span>Melbourne</span>
              </li>
              <li>
                <span class="nr_common_text_color">Contact Number:</span>
                <span>+61 836 474 755</span>
              </li>
              <li>
                <span class="nr_common_text_color">Promotion:</span>
                <span>15% of all Cleaning Services</span>
              </li>
            </ul>
            <button class="nr_club_btn nr-shop-card-btn">Avail Now</button>
          </div>
        </div>
        <div class="nr_points_card nr_pgb_gradient_border nr-shop-list-card">
          <div class="nr-shop-list-img">
            <img width="140" height="140" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/shop-list2.png?v=1741691267">
          </div>
          <div class="nr-shop-list-card-body">
            <span class="nr-shop-list-title nr_common_text_color">Rapid Tune Sunshine North</span>
            <ul class="nr-shop-list-ul">
              <li>
                <span class="nr_common_text_color">Location:</span>
                <span>196 Mcintyre Road, Sunshine North VIC 3020</span>
              </li>
              <li>
                <span class="nr_common_text_color">Contact Number:</span>
                <span>+03 9454 0888</span>
              </li>
              <li>
                <span class="nr_common_text_color">Contact Number:</span>
                <span>+61 836 474 755</span>
              </li>
              <li>
                <span class="nr_common_text_color">Promotion:</span>
                <span>25% off General Service & Log Book Service - (Include Oil, Oil Filter Replacement,
                Wheel Balance & Full Vehicle Inspection) and courtesy vehicle and drop off with in 5k area </span>
              </li>
            </ul>
            <button class="nr_club_btn nr-shop-card-btn">Avail Now</button>
          </div>
        </div>
        <div class="nr_points_card nr_pgb_gradient_border nr-shop-list-card">
          <div class="nr-shop-list-img">
            <img width="140" height="140" src="https://cdn.shopify.com/s/files/1/0668/2368/4143/files/shop-list3.png?v=1741691267">
          </div>
          <div class="nr-shop-list-card-body">
            <span class="nr-shop-list-title nr_common_text_color">Dynamic Therapy</span>
            <ul class="nr-shop-list-ul">
              <li>
                <span class="nr_common_text_color">Location:</span>
                <span>555 Sunshine Avenue, Taylors Lakes 3038 (Inside of The Lakes Gym)</span>
              </li>
              <li>
                <span class="nr_common_text_color">Contact Number:</span>
                <span>0482 070 141</span>
              </li>
              <li>
                <span class="nr_common_text_color">Personal (JD):</span>
                <span>0434 474 997</span>
              </li>
              <li>
                <span class="nr_common_text_color">Promotion:</span>
                <span>2 Free Treatments at Dynamic Therapy</span>
              </li>
            </ul>
            <button class="nr_club_btn nr-shop-card-btn">Avail Now</button>
          </div>
        </div>
      </div>
      
      <!-- SHOP CARD LIST END -->
      <span class="nr_dashboard_divider nr_menu_divider"></span>
      
      <!-- Order details -->
      <div id="order_detail">
        <!-- <div class="nr_club_card_inner">
          <div class="nr_club_card_content">
            <span class="nr_club_card_title nr_common_text_color">{{ 'customer.orders.title' | t }}</span>
          </div>
        </div> -->
        
      <div class="nr_points_card nr_club_card nr_pgb_gradient_border">
        
      <div class="nr_order_table_wrapper">
      <span class="nr_club_card_title nr_common_text_color">{{ 'customer.orders.title' | t }}</span>

      {% paginate customer.orders by 20 %}
        {%- if customer.orders.size > 0 -%}
          
            
            <div class="nr_order_table">
              <table role="table" class="order-history">
                <!-- <caption class="visually-hidden">
                  {{ 'customer.orders.title' | t }}
                </caption> -->
                <thead role="rowgroup">
                  <tr role="row">
                    <th id="ColumnOrder" scope="col" role="columnheader">{{ 'customer.orders.order_number' | t }}</th>
                    <th id="ColumnDate" scope="col" role="columnheader">{{ 'customer.orders.date' | t }}</th>
                    <th id="ColumnPayment" scope="col" role="columnheader">{{ 'customer.orders.payment_status' | t }}</th>
                    <th id="ColumnFulfillment" scope="col" role="columnheader">
                      {{ 'customer.orders.fulfillment_status' | t }}
                    </th>
                    <!-- <th id="ColumnEntries" scope="col" role="columnheader">Total Entries</th> -->
                    <th id="ColumnTotal" scope="col" role="columnheader">{{ 'customer.orders.total' | t }}</th>
                  </tr>
                </thead>
                <tbody role="rowgroup">
                  {%- for order in customer.orders -%}
                    <tr role="row">
                      <td
                        id="RowOrder"
                        role="cell"
                        headers="ColumnOrder"
                        data-label="{{ 'customer.orders.order_number' | t }}"
                      >
                        <a
                          href="{{ order.customer_url }}"
                          aria-label="{{ 'customer.orders.order_number_link' | t: number: order.name }}"
                          class="nr_table_link"
                        >
                          {{ order.name }}
                        </a>
                      </td>
                      <td headers="RowOrder ColumnDate" role="cell" data-label="{{ 'customer.orders.date' | t }}">
                        {{ order.created_at | time_tag: format: 'date' }}
                      </td>
                      <td
                        headers="RowOrder ColumnPayment"
                        role="cell"
                        data-label="{{ 'customer.orders.payment_status' | t }}"
                      >
                        {{ order.financial_status_label }}
                      </td>
                      <td
                        headers="RowOrder ColumnFulfillment"
                        role="cell"
                        data-label="{{ 'customer.orders.fulfillment_status' | t }}"
                      >
                        {{ order.fulfillment_status_label }}
                      </td>
                      {% comment %}
                        <td
                            headers="RowOrder ColumnEntries"
                            role="cell"
                            data-label="ColumnEntries"
                          >
                          {% if customer.metafields.custom.total_entries %}
                            {{ customer.metafields.custom.total_entries }}
                          {% else %}
                            0
                          {% endif %}  
                        </td>
                      {% endcomment %}
                      <td headers="RowOrder ColumnTotal" role="cell" data-label="{{ 'customer.orders.total' | t }}">
                        {{ order.total_net_amount | money_with_currency }}
                      </td>
                    </tr>
                  {%- endfor -%}
                </tbody>
              </table>
            </div>
          
        {%- else -%}
          <p class="nr_empty_order_history" >{{ 'customer.orders.none' | t }}</p>
        {%- endif -%}
      
        
        {%- if paginate.pages > 1 -%}
          {%- if paginate.parts.size > 0 -%}
            <nav class="pagination" role="navigation" aria-label="{{ 'general.pagination.label' | t }}">
              <ul role="list">
                {%- if paginate.previous -%}
                  <li>
                    <a href="{{ paginate.previous.url }}" aria-label="{{ 'general.pagination.previous' | t }}">
                      <span class="svg-wrapper">
                        {{- 'icon-caret.svg' | inline_asset_content -}}
                      </span>
                    </a>
                  </li>
                {%- endif -%}

                {%- for part in paginate.parts -%}
                  <li>
                    {%- if part.is_link -%}
                      <a href="{{ part.url }}" aria-label="{{ 'general.pagination.page' | t: number: part.title }}">
                        {{- part.title -}}
                      </a>
                    {%- else -%}
                      {%- if part.title == paginate.current_page -%}
                        <span aria-current="page" aria-label="{{ 'general.pagination.page' | t: number: part.title }}">
                          {{- part.title -}}
                        </span>
                      {%- else -%}
                        <span>{{ part.title }}</span>
                      {%- endif -%}
                    {%- endif -%}
                  </li>
                {%- endfor -%}

                {%- if paginate.next -%}
                  <li>
                    <a href="{{ paginate.next.url }}" aria-label="{{ 'general.pagination.next' | t }}">
                      <span class="svg-wrapper">
                        {{- 'icon-caret.svg' | inline_asset_content -}}
                      </span>
                    </a>
                  </li>
                {%- endif -%}
              </ul>
            </nav>
          {%- endif -%}
        {%- endif -%}
      {% endpaginate %}
        </div>
    </div>
</div>
    <!-- End of orders -->

      <span class="nr_dashboard_divider nr_menu_divider"></span>

<!-- Second Box: Membership Table -->
<div class="nr_points_card nr_club_card nr_pgb_gradient_border">
  <div class="nr_order_table_wrapper">
    <span class="nr_club_card_title nr_common_text_color">Membership Information</span>
    <div class="nr_order_table" id='tblMembership'>
      <table role="table" class="order-history" id="membershipTable">
        <thead role="rowgroup">
          <tr role="row">
            <th scope="col" role="columnheader">#</th>
            <th scope="col" role="columnheader">Plan Name</th>
            <th scope="col" role="columnheader">Membership From</th>
            <th scope="col" role="columnheader">Membership To</th>
            <th scope="col" role="columnheader">Price</th>
            <th scope="col" role="columnheader">Discount (%)</th>
            <th scope="col" role="columnheader">Free Entries</th>
            <th scope="col" role="columnheader">Accumulative Entries</th>
            <th scope="col" role="columnheader">Status</th>
          </tr>
        </thead>
        <tbody role="rowgroup" id="membershipTableBody">
          <!-- Dynamic rows will be injected here -->
        </tbody>
      </table>
    </div>
  </div>
</div>
    <!-- End of orders -->
      
    </div>
  </div>
</section>

<!-- Payment method confirmation -->

<style>
/* Modal backdrop */
.modal {
  display: none; /* Hidden by default */
  position: fixed;
  z-index: 1000;
  left: 0; top: 0;
  width: 100%; height: 100%;
  overflow: auto;
  background-color: rgba(0,0,0,0.5);
}

.modal-wrapper{
   display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
}

/* Modal content box */
.modal-content {
  background-color: #1c163a !important;
  margin: 10% auto;
  padding: 40px 20px;
  border-radius: 10px;
  width: 90%;
  max-width: 500px;
  text-align: center;
  border: 1px solid rgba(255, 255, 255, 0.4) !important;
  box-shadow: 0 4px 22px #f0d38f26 !important;
}

.modal-content h2, .modal-content p{
  background: linear-gradient(180deg, #fff, #e8ab1c);
  -webkit-text-fill-color: transparent;
  background-clip: text;
  font-size: 26px !important;
}

.modal-content p{
  font-size: 20px !important;
}
  
/* Buttons styling */
.modal-buttons {
  margin-top: 20px;
  display: flex;
    justify-content: center;
    gap: 20px;
}

.modal-buttons button {
  padding: 10px 15px;
  border-radius: 5px;
  border: none;
  cursor: pointer;
  min-width: 115px;
  line-height: 20px;
  height: 48px;
  font-size: 16px !important;
  line-height: 22px;
  font-weight: 500;
}

.offer-btn {
  background: linear-gradient(180deg, #ffe99a, #fcd343);
  color: #000;
  
}

.cancel-btn {
  background: linear-gradient(180deg, #6fb1e9, #5535f6);
  color: white;
}

  @media screen and (max-width: 575px){
    .modal-buttons {
      margin-top: 20px;
      display: flex;
      flex-direction: column;
    }
    .modal-buttons button {
      font-size: 14px !important;
      line-height: 16px;
    }
  }
</style>

<div id="confirm-popup" class="modal">
  <div class="modal-wrapper">
      <div class="modal-content">
        <h2>You have not verified your payment detail yet, it's required for next membership subscription. Do you want to activate it?</h2>
        <input type="hidden" name="stripe_url" id="stripe-url" value="">
        <div class="modal-buttons">
          <button id="confirm-yes-btn" class="offer-btn">Yes</button>
          <button id="confirm-no-btn" class="cancel-btn">No</button>
        </div>
      </div>
  </div>
</div>
<!-- End of Payment Method Confirmation -->





<!-- Added to fetch bonus points -->
<script>
    async function fetchBonusDetail() {
    return new Promise((resolve, reject) => {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://nobledev.wedowebapps.in/get-bonus-detail', true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onreadystatechange = function () {
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                  // alert('success..');
                    var resp = JSON.parse(xhr.responseText);
                    console.log("------------");
                    console.log(resp);
                    console.log("Received Bonus : " + resp.received_bonus);  
                    console.log("------------");
                    document.getElementById("receivedBonuPoint").textContent = resp.received_bonus;
                    document.getElementById("appBonusPoints").value = resp.received_bonus;

                    if(resp.received_bonus > 0){
                      document.getElementById("divBonusPoints").style.display = "block";
                    }
                  
                    // if (resp.code === 'Success') {
                    //   alert('testing...');
                    //     // targetDate = new Date(resp.message).getTime();
                    //     // console.log("Target date from fetch function:", targetDate);
                    //     // resolve(targetDate); // Resolve the Promise with the target date
                    //     console.log("Received Bonus : " + resp.received_bonus);
                    // } else {
                    //     reject("Invalid response: " + resp.message);
                    // }
                } else {
                    reject("HTTP error: " + xhr.status);
                }
            }
        };
  
         xhr.send(JSON.stringify({
            customer_id: {{ customer.id }}
        }));
    });
  }
  fetchBonusDetail();

  // ============== Apply Bonus Points =================
  function applyBonusPoints() {
      var totalBonusPoints = document.getElementById("appBonusPoints").value;
      var applyOn = document.getElementById("bonus_applicable_on").value;
      if(applyOn == ''){
        showToast('Please select month you want to apply bonus points');
        return;
      }
      var customer_id = {{ customer.id }};
      
      // If all fields are valid, proceed
      if (totalBonusPoints > 0) {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://nobledev.wedowebapps.in/apply-bonus-detail', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
          console.log("XHR Response : ");
          console.log(xhr.responseText);
          console.log("---------------");
          
          var resp = JSON.parse(xhr.responseText);
          console.log("------------");
          console.log(resp);
          console.log("------------");
          showToast(resp.message);
          // if(resp.code == 0) {
          //   showToast("Bonus points applied successfully.");
          //   // window.location.href = "/";
          // } 
        }
      };
      var data = {bonus_points: totalBonusPoints, customer_id: customer_id, apply_on: applyOn};
      xhr.send(JSON.stringify(data));
      // window.location.href = "/account/login";
    }
  }
  // End ========== Apply Bonus Points ============

  // ============== Continue Membership =================
  function fetchMembershipDetail() {
    var customer_id = {{ customer.id }};
    var xhr = new XMLHttpRequest();
    xhr.open('POST', 'https://nobledev.wedowebapps.in/get-membership-detail', true);
    xhr.setRequestHeader('Content-Type', 'application/json');
    xhr.onreadystatechange = function() {
      if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
        console.log("XHR Response : ");
        console.log(xhr.responseText);
        console.log("---------------");
        const resp = JSON.parse(xhr.responseText);
        if(resp.length > 0){
          const tbody = document.getElementById("membershipTableBody");

          // Helper function to format date
          function formatDate(dateString) {
            const [year, month, day] = dateString.split(' ')[0].split('-');
            return `${day}-${month}-${year}`;
          }

          resp.forEach((member, index) => {
            const seqNo = index + 1;
            const row = `<tr role="row">
              <td role="cell">${seqNo}</td>
              <td role="cell">${member.plan_name}</td>
              <td role="cell">${formatDate(member.membership_from)}</td>
              <td role="cell">${formatDate(member.membership_to)}</td>
              <td role="cell">$ ${member.applicable_price}</td>
              <td role="cell">${member.applicable_discount ?? '-'}</td>
              <td role="cell">${member.applicable_entries}</td>
              <td role="cell">${member.applicable_acm_entries}</td>
              <td role="cell">${member.membership_status}</td>
            </tr>`;
            tbody.insertAdjacentHTML('beforeend', row);
          });
        }else{
          document.getElementById('tblMembership').innerHTML='<p style="color:#FFF!important;">No subscriptions for membership yet.</p>';
        }
      }
    };
    var data = { customer_id: customer_id };
    xhr.send(JSON.stringify(data));
  }
  fetchMembershipDetail();
  // End ========== Continue Membership ============

  // ==============Update Membership Dates =================
  function updateMembershipDates() {
      var customer_id = {{ customer.id }};
      
      // If all fields are valid, proceed
      // if (totalBonusPoints > 0) {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://nobledev.wedowebapps.in/update-membership-dates', true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
          console.log("XHR Response : ");
          console.log(xhr.responseText);
          console.log("---------------");
          
          var resp = JSON.parse(xhr.responseText);
          console.log("------------");
          console.log(resp);
          console.log("------------");
          
          showToast(resp.message);
          window.location.href = "/account";
        }
      };
      var data = {customer_id: customer_id};
      xhr.send(JSON.stringify(data));
      // window.location.href = "/account/login";
    // }
  }
  // End ========== Apply Bonus Points ============

  // ================ Check payment method ============
  async function checkPaymentMethod() {
    // alert('check payment method')
    return new Promise((resolve, reject) => {
        var xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://nobledev.wedowebapps.in/check-payment-method', true);
        xhr.setRequestHeader('Content-Type', 'application/json');

        xhr.onreadystatechange = function () {
            // alert({{ customer.id }});
            if (xhr.readyState === XMLHttpRequest.DONE) {
                if (xhr.status === 200) {
                    // alert('success..');
                    var resp = JSON.parse(xhr.responseText);
                    // alert(resp.code);
                    console.log("------------");
                    console.log(resp);
                    console.log("Received Message : " + resp.message);  
                    console.log("------------");
                    //document.getElementById("receivedBonuPoint").textContent = resp.received_bonus;
                    // document.getElementById("appBonusPoints").value = resp.received_bonus;

                    if(resp.code == 'Fail'){
                      // alert(resp.message);
                      document.getElementById('confirm-popup').style.display = 'block';
                      document.getElementById('stripe-url').value=resp.message;
                    }
                } else {
                    reject("HTTP error: " + xhr.status);
                }
            }
        };
  
         xhr.send(JSON.stringify({
            customer_id: {{ customer.id }}
        }));
    });
  }
  checkPaymentMethod();
  // =============== End of checking payment method ========================


function handlePaymentMethod(action) {
    const customer_id = {{ customer.id }};
    var xhr = new XMLHttpRequest();
        xhr.open('POST', 'https://nobledev.wedowebapps.in/membership/' + action, true);
        xhr.setRequestHeader('Content-Type', 'application/json');
        xhr.onreadystatechange = function() {
        if (xhr.readyState === XMLHttpRequest.DONE && xhr.status === 200) {
          console.log("XHR Response : ");
          console.log(xhr.responseText);
          console.log("---------------");
          
          var resp = JSON.parse(xhr.responseText);
          console.log("------------");
          console.log(resp);
          console.log("------------");

          if(resp.status){
            showToast(resp.message);
            // window.location.href = "/account";
          }else{
            showToast(resp.message);
          }
        }
        document.getElementById('cancel-popup').style.display = 'none';
      };
      var data = {customer_id: customer_id, action: action};
      xhr.send(JSON.stringify(data));
}

// document.getElementById('continue-offer-btn').addEventListener('click', function() {
//   handleMembershipAction('offer');
// });

// If "Yes" Show second offer modal
document.getElementById('confirm-yes-btn').addEventListener('click', function() {
  // document.getElementById('confirm-popup').style.display = 'none';
  // document.getElementById('cancel-popup').style.display = 'block';
  window.location.href = document.getElementById('stripe-url').value; 
});

// If "No" Close first popup
document.getElementById('confirm-no-btn').addEventListener('click', function() {
  document.getElementById('confirm-popup').style.display = 'none';
});




  
</script>
<!-- End of getting bonus points -->

{% schema %}
{
  "name": "t:sections.main-account.name",
  "settings": [
    {
      "type": "header",
      "content": "t:sections.all.padding.section_padding_heading"
    },
    {
      "type": "range",
      "id": "padding_top",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_top",
      "default": 36
    },
    {
      "type": "range",
      "id": "padding_bottom",
      "min": 0,
      "max": 100,
      "step": 4,
      "unit": "px",
      "label": "t:sections.all.padding.padding_bottom",
      "default": 36
    }
  ]
}
{% endschema %}
